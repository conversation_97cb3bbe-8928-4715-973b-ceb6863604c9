#!/usr/bin/env python3
"""
Test runner for MetricaX library.

This script runs all tests and provides a summary of results.
"""

import sys
import os
import subprocess

def run_tests():
    """Run all tests and display results."""
    print("🧪 Running MetricaX Test Suite")
    print("=" * 40)
    
    # Check if pytest is available
    try:
        import pytest
    except ImportError:
        print("❌ pytest not found. Install with:")
        print("   pip install pytest")
        return False
    
    # Run tests
    test_dir = os.path.join(os.path.dirname(__file__), 'tests')
    
    if not os.path.exists(test_dir):
        print("❌ Tests directory not found")
        return False
    
    # Run pytest with verbose output
    cmd = [sys.executable, '-m', 'pytest', test_dir, '-v', '--tb=short']
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Warnings/Errors:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ All tests passed!")
            return True
        else:
            print(f"\n❌ Tests failed (exit code: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def run_examples():
    """Run example scripts to verify they work."""
    print("\n🎯 Running Example Scripts")
    print("=" * 30)
    
    examples_dir = os.path.join(os.path.dirname(__file__), 'examples')
    
    if not os.path.exists(examples_dir):
        print("❌ Examples directory not found")
        return False
    
    examples = [
        'ab_testing_example.py',
        'spam_filter_example.py', 
        'data_update_example.py'
    ]
    
    success_count = 0
    
    for example in examples:
        example_path = os.path.join(examples_dir, example)
        
        if not os.path.exists(example_path):
            print(f"⚠️  {example} not found")
            continue
        
        try:
            print(f"Running {example}...")
            result = subprocess.run([sys.executable, example_path], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ {example} - Success")
                success_count += 1
            else:
                print(f"❌ {example} - Failed")
                if result.stderr:
                    print(f"   Error: {result.stderr[:200]}...")
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ {example} - Timeout")
        except Exception as e:
            print(f"❌ {example} - Error: {e}")
    
    print(f"\n📊 Examples: {success_count}/{len(examples)} successful")
    return success_count == len(examples)

if __name__ == "__main__":
    print("🚀 MetricaX Quality Assurance")
    print("=" * 50)
    
    tests_passed = run_tests()
    examples_passed = run_examples()
    
    print("\n" + "=" * 50)
    print("📋 Summary:")
    print(f"   Tests: {'✅ PASSED' if tests_passed else '❌ FAILED'}")
    print(f"   Examples: {'✅ PASSED' if examples_passed else '❌ FAILED'}")
    
    if tests_passed and examples_passed:
        print("\n🎉 All quality checks passed! MetricaX is ready to use.")
        sys.exit(0)
    else:
        print("\n⚠️  Some quality checks failed. Please review the output above.")
        sys.exit(1)
