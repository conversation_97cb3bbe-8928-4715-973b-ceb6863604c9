"""
MetricaX - Professional Bayesian Statistics Toolkit for Python.

This package provides production-ready tools for Bayesian inference including:
- Beta distributions and statistical moments
- <PERSON><PERSON>' theorem applications
- Conjugate prior updates
- Numerical utilities and validation
"""

__version__ = "0.1.0"
__author__ = "MetricaX Team"

# Import the main bayesian module
from . import bayesian

__all__ = [
    "bayesian",
]
