# MetricaX

A comprehensive mathematical and statistical library for Python, providing tools for Bayesian statistics, information theory, and numerical computations.

## Features

- **Bayesian Statistics**: Beta distributions, <PERSON><PERSON>' theorem, conjugate priors
- **Information Theory**: Entropy, mutual information, and related measures
- **Numerical Utilities**: Safe mathematical operations and validation functions

## Installation

```bash
pip install metricax
```

## Quick Start

```python
import metricax.bayesian as mb

# Beta distribution
pdf_value = mb.beta_pdf(0.5, 2, 2)
mean_value = mb.beta_mean(2, 2)

# Bayesian inference
posterior = mb.bayes_posterior(0.3, 0.8, 0.5)

# Conjugate priors
alpha_new, beta_new = mb.update_beta_binomial(1, 1, 7, 3)
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Support

For questions and support:
- GitHub Issues: [https://github.com/metricax/metricax/issues](https://github.com/metricax/metricax/issues)
- Documentation: [https://metricax.readthedocs.io](https://metricax.readthedocs.io)