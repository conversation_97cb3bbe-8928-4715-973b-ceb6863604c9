# MetricaX 🎯

**Professional Bayesian Statistics Toolkit for Python**

MetricaX is a production-ready library for Bayesian inference, designed for data scientists, researchers, and engineers who need reliable statistical computations with real-world applications.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)

## 🚀 Why MetricaX?

- **Production-Ready**: Comprehensive error handling and numerical stability
- **Self-Contained Modules**: Each topic (Bayesian, etc.) is completely independent
- **Real-World Examples**: A/B testing, spam filtering, quality control
- **Professional**: Full test suite, type hints, detailed documentation
- **Pure Python**: No heavy dependencies, easy to integrate
- **Scalable**: Easy to add new mathematical modules

## 📦 Installation

```bash
pip install metricax
```

## ⚡ Quick Start

```python
import metricax.bayesian as mb

# A/B Testing Analysis
control_alpha, control_beta = mb.update_beta_binomial(1, 1, 12, 108)  # 12/120 conversions
treatment_alpha, treatment_beta = mb.update_beta_binomial(1, 1, 15, 85)  # 15/100 conversions

print(f"Control conversion rate: {mb.beta_mean(control_alpha, control_beta):.3f}")
print(f"Treatment conversion rate: {mb.beta_mean(treatment_alpha, treatment_beta):.3f}")

# Bayesian Spam Filter
priors = [0.4, 0.6]  # 40% spam, 60% ham
likelihoods = [0.8, 0.1]  # P(word|spam), P(word|ham)
posteriors = mb.bayes_update_discrete(priors, likelihoods)
print(f"Probability of spam: {posteriors[0]:.3f}")
```

## 🎯 Real-World Applications

### 📊 A/B Testing
```python
# Compare conversion rates with statistical confidence
from metricax.bayesian.examples import ab_testing
ab_testing.run_example()
```

### 📧 Spam Detection
```python
# Build a Bayesian spam filter
from metricax.bayesian.examples import spam_filter
spam_filter.run_example()
```

### 🏭 Quality Control
```python
# Monitor manufacturing defect rates
from metricax.bayesian.examples import data_updates
data_updates.manufacturing_quality_control()
```

## 📚 Complete API Reference

### Beta Distributions (5 functions)
- `beta_pdf(x, alpha, beta)` - Probability density function
- `beta_cdf(x, alpha, beta)` - Cumulative distribution function
- `beta_mean(alpha, beta)` - Distribution mean
- `beta_var(alpha, beta)` - Distribution variance
- `beta_mode(alpha, beta)` - Distribution mode

### Bayes' Theorem (4 functions)
- `bayes_posterior(prior, likelihood, marginal)` - Basic Bayes rule
- `bayes_odds(prior_odds, likelihood_ratio)` - Odds form
- `bayes_update_discrete(priors, likelihoods)` - Discrete updates
- `marginal_likelihood_discrete(priors, likelihoods)` - Marginal likelihood

### Conjugate Priors (3 functions)
- `update_beta_binomial(alpha, beta, successes, failures)` - Beta-Binomial
- `update_normal_known_variance(mu0, sigma0, data)` - Normal updates
- `update_poisson_gamma(alpha, beta, observed_sum, n_obs)` - Poisson-Gamma

### Utilities (4 functions)
- `gamma_func(x)` - Gamma function
- `validate_prob(x)` - Probability validation
- `normalize(probs)` - Probability normalization
- `safe_div(a, b)` - Safe division

**Total: 16 production-ready functions**

## 🧪 Testing

Run the comprehensive test suite:

```bash
cd metricax
python -m pytest tests/ -v
```

## 📖 Examples

Explore real-world applications in the `examples/` directory:

- `ab_testing_example.py` - Complete A/B testing workflow
- `spam_filter_example.py` - Bayesian spam classification
- `data_update_example.py` - Online learning scenarios

## 🏗️ Project Structure

```
metricax/
├── LICENSE
├── README.md
├── pyproject.toml
└── metricax/
    ├── __init__.py                    # Main package entry
    └── bayesian/                      # 🎯 Complete Bayesian Module
        ├── __init__.py                # 16 functions exported
        ├── beta_distributions.py      # Beta distribution functions
        ├── bayes_theorem.py           # Bayes' theorem applications
        ├── conjugate_priors.py        # Conjugate prior updates
        ├── utils.py                   # Utility functions
        ├── examples/                  # Self-contained examples
        │   ├── README.md              # Examples documentation
        │   ├── ab_testing.py          # A/B testing analysis
        │   ├── spam_filter.py         # Spam classification
        │   └── data_updates.py        # Online learning
        └── tests/                     # Self-contained tests
            ├── test_beta_distributions.py
            ├── test_bayes_theorem.py
            └── test_utils.py
```

### 🚀 **Modular Design Benefits:**
- **Self-Contained**: Each module (bayesian) includes code, examples, and tests
- **Scalable**: Easy to add new modules like `optimization/`, `statistics/`, etc.
- **Independent**: Modules don't interfere with each other
- **Professional**: Industry-standard organization

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **GitHub Issues**: [Report bugs or request features](https://github.com/metricax/metricax/issues)
- **Documentation**: [Full API documentation](https://metricax.readthedocs.io)
- **Examples**: Check the `examples/` directory for real-world use cases

---

**Built with ❤️ for the data science community**