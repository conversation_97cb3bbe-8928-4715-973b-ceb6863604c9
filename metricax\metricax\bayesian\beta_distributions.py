import math
from typing import Union

def beta_pdf(x: float, alpha: float, beta: float) -> float:
    """
    Compute the Beta probability density function (PDF).

    Time Complexity: O(1)
    Space Complexity: O(1)

    Parameters:
        x (float): Value in [0, 1]
        alpha (float): Shape parameter alpha > 0
        beta (float): Shape parameter beta > 0

    Returns:
        float: PDF value at x
    """
    if not 0 <= x <= 1:
        raise ValueError("x must be in [0, 1]")
    if alpha <= 0 or beta <= 0:
        raise ValueError("alpha and beta must be > 0")

    try:
        numerator = x**(alpha - 1) * (1 - x)**(beta - 1)
        denominator = math.gamma(alpha) * math.gamma(beta) / math.gamma(alpha + beta)
        return numerator / denominator
    except OverflowError:
        return 0.0


def beta_log_pdf(x: float, alpha: float, beta: float) -> float:
    """
    Compute the natural logarithm of the Beta PDF (useful for log-space calculations).

    Time Complexity: O(1)
    Space Complexity: O(1)

    Returns:
        float: Log PDF value at x
    """
    if not 0 < x < 1:
        return float("-inf")
    log_pdf = ((alpha - 1) * math.log(x) +
               (beta - 1) * math.log(1 - x) -
               math.lgamma(alpha) - math.lgamma(beta) +
               math.lgamma(alpha + beta))
    return log_pdf


def beta_cdf(x: float, alpha: float, beta: float, steps: int = 1000) -> float:
    """
    Approximate the CDF of the Beta distribution using the trapezoidal rule.

    Time Complexity: O(n)
    Space Complexity: O(1)

    Parameters:
        steps (int): Number of integration slices. Higher = more accurate.

    Returns:
        float: Approximate CDF at x
    """
    if not 0 <= x <= 1:
        raise ValueError("x must be in [0, 1]")
    
    dx = x / steps
    total = 0.5 * beta_pdf(0, alpha, beta)
    for i in range(1, steps):
        total += beta_pdf(i * dx, alpha, beta)
    total += 0.5 * beta_pdf(x, alpha, beta)
    return total * dx


def beta_mean(alpha: float, beta: float) -> float:
    """
    Compute the mean of the Beta distribution.

    Time Complexity: O(1)
    """
    if alpha <= 0 or beta <= 0:
        raise ValueError("alpha and beta must be > 0")
    return alpha / (alpha + beta)


def beta_variance(alpha: float, beta: float) -> float:
    """
    Compute the variance of the Beta distribution.

    Time Complexity: O(1)
    """
    if alpha <= 0 or beta <= 0:
        raise ValueError("alpha and beta must be > 0")
    a_b = alpha + beta
    return (alpha * beta) / (a_b**2 * (a_b + 1))


def beta_mode(alpha: float, beta: float) -> Union[float, str]:
    """
    Compute the mode of the Beta distribution.

    Only valid if alpha > 1 and beta > 1.

    Time Complexity: O(1)
    """
    if alpha <= 1 or beta <= 1:
        return "Mode undefined for alpha <= 1 or beta <= 1"
    return (alpha - 1) / (alpha + beta - 2)


def beta_median_approx(alpha: float, beta: float) -> float:
    """
    Approximate the median of the Beta distribution.

    This uses the approximation by:
        Median ≈ (alpha - 1/3) / (alpha + beta - 2/3)

    Time Complexity: O(1)
    """
    if alpha <= 0 or beta <= 0:
        raise ValueError("alpha and beta must be > 0")
    return (alpha - 1/3) / (alpha + beta - 2/3)
