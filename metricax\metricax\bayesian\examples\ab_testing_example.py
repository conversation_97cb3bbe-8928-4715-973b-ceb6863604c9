#!/usr/bin/env python3
"""
A/B Testing with Bayesian Analysis using MetricaX

This example demonstrates how to use MetricaX for A/B testing analysis,
comparing conversion rates between two website variants using Beta distributions.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import metricax.bayesian as mb
import matplotlib.pyplot as plt
import numpy as np

def ab_test_analysis():
    """
    Real-world A/B testing scenario:
    - Variant A (Control): 120 visitors, 12 conversions
    - Variant B (Treatment): 100 visitors, 15 conversions
    """
    
    print("🧪 A/B Testing with Bayesian Analysis")
    print("=" * 50)
    
    # Data from our A/B test
    visitors_a, conversions_a = 120, 12
    visitors_b, conversions_b = 100, 15
    
    print(f"Variant A (Control): {conversions_a}/{visitors_a} conversions")
    print(f"Variant B (Treatment): {conversions_b}/{visitors_b} conversions")
    print()
    
    # Prior beliefs (uninformative prior: Beta(1,1) = Uniform)
    prior_alpha, prior_beta = 1, 1
    
    # Update beliefs with observed data using conjugate priors
    alpha_a, beta_a = mb.update_beta_binomial(
        prior_alpha, prior_beta, 
        conversions_a, visitors_a - conversions_a
    )
    
    alpha_b, beta_b = mb.update_beta_binomial(
        prior_alpha, prior_beta,
        conversions_b, visitors_b - conversions_b
    )
    
    print("📊 Posterior Distributions:")
    print(f"Variant A: Beta({alpha_a}, {beta_a})")
    print(f"Variant B: Beta({alpha_b}, {beta_b})")
    print()
    
    # Calculate posterior statistics
    mean_a = mb.beta_mean(alpha_a, beta_a)
    mean_b = mb.beta_mean(alpha_b, beta_b)
    var_a = mb.beta_var(alpha_a, beta_a)
    var_b = mb.beta_var(alpha_b, beta_b)
    
    print("📈 Conversion Rate Estimates:")
    print(f"Variant A: {mean_a:.3f} ± {np.sqrt(var_a):.3f}")
    print(f"Variant B: {mean_b:.3f} ± {np.sqrt(var_b):.3f}")
    print()
    
    # Calculate probability that B > A
    # Using Monte Carlo simulation
    n_samples = 10000
    samples_a = np.random.beta(alpha_a, beta_a, n_samples)
    samples_b = np.random.beta(alpha_b, beta_b, n_samples)
    prob_b_better = np.mean(samples_b > samples_a)
    
    print("🎯 Decision Analysis:")
    print(f"Probability that B > A: {prob_b_better:.3f}")
    
    if prob_b_better > 0.95:
        print("✅ Strong evidence that B is better than A")
    elif prob_b_better > 0.8:
        print("⚠️  Moderate evidence that B is better than A")
    elif prob_b_better < 0.2:
        print("❌ Strong evidence that A is better than B")
    else:
        print("🤔 Inconclusive - need more data")
    
    print()
    print("💡 Business Recommendation:")
    if prob_b_better > 0.8:
        lift = (mean_b - mean_a) / mean_a * 100
        print(f"   Implement Variant B (estimated {lift:+.1f}% lift)")
    else:
        print("   Continue testing or stick with Variant A")

def plot_distributions():
    """Plot the posterior distributions for visualization"""
    try:
        # Posterior parameters from the analysis above
        alpha_a, beta_a = 13, 109  # 1 + 12, 1 + (120-12)
        alpha_b, beta_b = 16, 86   # 1 + 15, 1 + (100-15)
        
        x = np.linspace(0, 0.3, 1000)
        
        # Calculate PDFs
        pdf_a = [mb.beta_pdf(xi, alpha_a, beta_a) for xi in x]
        pdf_b = [mb.beta_pdf(xi, alpha_b, beta_b) for xi in x]
        
        plt.figure(figsize=(10, 6))
        plt.plot(x, pdf_a, label='Variant A (Control)', linewidth=2)
        plt.plot(x, pdf_b, label='Variant B (Treatment)', linewidth=2)
        plt.axvline(mb.beta_mean(alpha_a, beta_a), color='blue', linestyle='--', alpha=0.7)
        plt.axvline(mb.beta_mean(alpha_b, beta_b), color='orange', linestyle='--', alpha=0.7)
        
        plt.xlabel('Conversion Rate')
        plt.ylabel('Probability Density')
        plt.title('Posterior Distributions of Conversion Rates')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
    except ImportError:
        print("📊 Install matplotlib and numpy to see the visualization:")
        print("   pip install matplotlib numpy")

if __name__ == "__main__":
    ab_test_analysis()
    print("\n" + "="*50)
    plot_distributions()
