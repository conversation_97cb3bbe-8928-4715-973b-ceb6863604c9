import sys
import os
sys.path.insert(0, os.path.join(os.getcwd(), 'metricax'))

print("Testing metricax.bayesian module...")

try:
    # Test individual imports
    from metricax.bayesian.utils import validate_prob, normalize, safe_div, gamma_func
    print("✅ Utils imported successfully")
    
    from metricax.bayesian.beta_distributions import beta_pdf, beta_mean
    print("✅ Beta distributions imported successfully")
    
    from metricax.bayesian.bayes_theorem import bayes_posterior
    print("✅ Bayes theorem imported successfully")
    
    from metricax.bayesian.conjugate_priors import update_beta_binomial
    print("✅ Conjugate priors imported successfully")
    
    # Test main module import
    import metricax.bayesian as mb
    print("✅ Main module imported successfully")
    
    # Quick functionality test
    result = mb.beta_mean(2, 2)
    print(f"✅ Function test: beta_mean(2, 2) = {result}")
    
    print("\n🎉 All imports and basic tests successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
