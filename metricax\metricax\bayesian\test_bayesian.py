#!/usr/bin/env python3
"""
Test script for metricax.bayesian module.

This script tests the basic functionality of all implemented functions.
"""

import sys
import os

# Add the metricax directory to the path
sys.path.insert(0, 'metricax')

try:
    import metricax.bayesian as mb
    print("✅ Successfully imported metricax.bayesian")
    print(f"📊 Available functions: {len(mb.__all__)}")
    print()
    
    # Test Beta distribution functions
    print("🧪 Testing Beta Distribution Functions:")
    print(f"beta_pdf(0.5, 2, 2) = {mb.beta_pdf(0.5, 2, 2):.4f}")
    print(f"beta_cdf(0.5, 2, 2) = {mb.beta_cdf(0.5, 2, 2):.4f}")
    print(f"beta_mean(2, 2) = {mb.beta_mean(2, 2):.4f}")
    print(f"beta_var(2, 2) = {mb.beta_var(2, 2):.4f}")
    print(f"beta_mode(2, 2) = {mb.beta_mode(2, 2):.4f}")
    print()
    
    # Test Bayes theorem functions
    print("🧪 Testing Bayes Theorem Functions:")
    print(f"bayes_posterior(0.3, 0.8, 0.5) = {mb.bayes_posterior(0.3, 0.8, 0.5):.4f}")
    print(f"bayes_odds(1.0, 2.0) = {mb.bayes_odds(1.0, 2.0):.4f}")
    
    priors = [0.3, 0.7]
    likelihoods = [0.8, 0.2]
    posteriors = mb.bayes_update_discrete(priors, likelihoods)
    print(f"bayes_update_discrete({priors}, {likelihoods}) = {[round(p, 4) for p in posteriors]}")
    
    marginal = mb.marginal_likelihood_discrete(priors, likelihoods)
    print(f"marginal_likelihood_discrete({priors}, {likelihoods}) = {marginal:.4f}")
    print()
    
    # Test conjugate priors
    print("🧪 Testing Conjugate Prior Functions:")
    alpha_new, beta_new = mb.update_beta_binomial(1, 1, 7, 3)
    print(f"update_beta_binomial(1, 1, 7, 3) = ({alpha_new}, {beta_new})")
    
    mu_new, sigma_new = mb.update_normal_known_variance(0, 1, [1, 2, 3], 1)
    print(f"update_normal_known_variance(0, 1, [1,2,3], 1) = ({mu_new:.4f}, {sigma_new:.4f})")
    
    alpha_new, beta_new = mb.update_poisson_gamma(2, 1, 15, 5)
    print(f"update_poisson_gamma(2, 1, 15, 5) = ({alpha_new}, {beta_new})")
    print()
    
    # Test utility functions
    print("🧪 Testing Utility Functions:")
    print(f"gamma_func(3.0) = {mb.gamma_func(3.0):.4f}")
    print(f"validate_prob(0.5) = {mb.validate_prob(0.5)}")
    print(f"validate_prob(1.5) = {mb.validate_prob(1.5)}")
    print(f"normalize([1, 2, 3]) = {[round(p, 4) for p in mb.normalize([1, 2, 3])]}")
    print(f"safe_div(10, 2) = {mb.safe_div(10, 2):.4f}")
    print(f"safe_div(10, 0) = {mb.safe_div(10, 0):.4f}")
    print()
    
    print("🎉 All tests completed successfully!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
